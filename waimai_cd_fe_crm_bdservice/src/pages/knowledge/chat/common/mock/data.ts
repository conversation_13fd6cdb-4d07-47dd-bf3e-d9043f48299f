export const mockData = {
    // 图文消息
    imgData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135622',
            msgId: '5135632',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent:
                '[{"type":"text","insert":"若商家未收到下线清退短信链接可让商家在商家端查看站内消息并确认，如图所示：\\n"},{"type":"image","insert":{"image":"https://s3plus.sankuai.com/bdaiassistant-public/image_1749542173900-20250610.png"}},{"type":"image","insert":{"image":"https://km.sankuai.com/api/file/cdn/1429409002/14123602886111?contentType=0&isNewContent=false"}},{"type":"text","insert":"\\n预计7月小蜜会接入下线清退短信链接，敬请期待~"}]',
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
    // 表单数据
    formDataWithRegex: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135622',
            msgId: '5135633',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '商家名称',
                                    type: 'input',
                                    defaultValue: '',
                                    tooltip: '请输入您的商家名称',
                                    labelWrap: false,
                                    regExp: '^.{2,50}$',
                                    message: '商家名称长度应在2-50个字符之间',
                                },
                                {
                                    label: '联系电话',
                                    type: 'input',
                                    defaultValue: '',
                                    tooltip: '请输入您的联系电话，方便我们与您联系',
                                    labelWrap: false,
                                    regExp: '^1[3-9]\\d{9}$',
                                    message: '请输入正确的手机号码格式',
                                },
                                {
                                    label: 'BDmis',
                                    type: 'input',
                                    defaultValue: 'jiangao',
                                },
                                {
                                    label: '绩效目标',
                                    type: 'radio',
                                    options: ['100%', '130%', '150%'],
                                    defaultValue: '100%',
                                },
                            ],
                            buttonText: '提交',
                            title: '新签攻克绩效',
                            subTitle: '我们已经了解到您要分析本月新签攻克绩效数据。请补充相关信息。',
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
    // option 选项
    optionData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '96163',
            msgId: '96166',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent:
                '[{"type":"text","insert":"请选择需要查询的场景"},{"type":"options","insert":{"options":{"options":[{"abilityType":1,"operationType":2,"content":"外卖入驻（切换客户kp签约）"},{"abilityType":1,"operationType":2,"content":"下线清退确认"},{"abilityType":1,"operationType":2,"content":"拼好饭签约"},{"abilityType":1,"operationType":2,"content":"美食城承诺书签约"},{"abilityType":1,"operationType":2,"content":"重新建店签约"}],"tabs":[{"label":"上线&清退","value":"上线&清退"},{"label":"配送/结算/推广","value":"配送/结算/推广"},{"label":"切换客户","value":"切换客户"}]}}}]',
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1753253839883,
            tags: null,
        },
    },
};
