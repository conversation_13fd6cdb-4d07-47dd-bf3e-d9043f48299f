import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    Animated,
    LayoutAnimation,
    PanResponder,
    Platform,
    StyleSheet,
    TouchableOpacity,
    View,
    Dimensions,
} from '@mrn/react-native';
import { useDebounceFn, useGetState } from 'ahooks';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';

import useNotify from './hooks/useNotify';
import useAssistantClose from '../../hooks/useAssistantClose';
import useGrayInfo from '../../hooks/useGrayInfo';
import { useLayout } from '../../hooks/useLayout';
import { useUser } from '../../store/user';
import { SOURCE } from '../../types';
import { startAnimation } from '../../utils/animation';
import openChat from '../../utils/openChat';
import { track, trackEvent, TrackEventType } from '../../utils/track';
import AssistantIcon from '../AssistantIcon';
import TaskReminderBubble from '../TaskReminderBubble';

const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');

interface Assistant {
    scrollY?: Animated.Value;
    source?: SOURCE | string;
    defaultPosition?: {
        top?: number;
        left?: number;
        right?: number;
        bottom?: number;
    };
}

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        zIndex: 1000,
        elevation: 1000,
    },
    icon: {
        ...Platform.select({
            ios: {
                shadowColor: '#171717',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.4,
                shadowRadius: 8,
            },
            android: {
                borderRadius: 55,
                backgroundColor: '#fff',
            },
        }),
    },
});

const ICON_SIZE = 55;
const HORIZONTAL_PADDING = 10;
const Assistant = (props: Assistant) => {
    const source = (props.source || SOURCE.home) as SOURCE;
    const { bottom } = useSafeAreaInsets();
    const bottomTabHeight = Platform.select({
        ios: bottom + 49,
        android: bottom + 49,
    });
    const isPoiPage = [
        SOURCE.wdcAdopt,
        SOURCE.wdcNearby,
        SOURCE.wdcAll,
        SOURCE.wdcResponsible,
        SOURCE.wdcConcerned,
    ].includes(source);
    const isMainPage = [
        SOURCE.wdcAdopt,
        SOURCE.wdcNearby,
        SOURCE.wdcAll,
        SOURCE.wdcResponsible,
        SOURCE.wdcConcerned,
        SOURCE.home,
        SOURCE.tabMine,
        SOURCE.tabWorkbench,
    ].includes(source);
    const baseTop = isPoiPage ? 64 : 0;
    const BASE_PADDING = 20;
    const bounds = {
        bottom: isMainPage
            ? screenHeight - (bottomTabHeight + ICON_SIZE + BASE_PADDING)
            : screenHeight - (ICON_SIZE + BASE_PADDING),
        top: 40 + baseTop + BASE_PADDING,
    };

    const { user } = useUser();
    const timer = useRef(null);
    const pan = useRef(new Animated.ValueXY()).current;
    const opacity = useRef(new Animated.Value(1)).current;
    const { layout, onLayout } = useLayout({ delay: 0 });
    const [visible, setVisible] = useState(false);
    const { isShowTaskReminder, hideTaskReminder } = useNotify(visible, source);

    const animationPlaying = useRef(false);
    const grayInfo = useGrayInfo();

    const [moving, setMoving] = useState(false);

    const handleAssistantIconPress = useDebounceFn(
        () => {
            trackEvent('icon_click', { misId: user.misId }, TrackEventType.MC, {
                cid: 'c_waimai_e_bee_rn_assistant_icon ',
            });

            openChat(source);
            hideTaskReminder();
            return;
        },
        { wait: 200 },
    );

    const handleTaskReminderPress = useDebounceFn(
        () => {
            openChat(source, { openTaskDrawer: true });
            hideTaskReminder();
        },
        { wait: 200 },
    );

    const foldIcon = () => {
        // animation playing, 不必再次执行，避免打断动画
        if (animationPlaying.current) {
            return;
        }
        animationPlaying.current = true;
        const position = getPosition();
        Animated.spring(
            pan, // Auto-multiplexed
            {
                toValue: { x: 45 * (position.right ? 1 : -1), y: 0 },
                useNativeDriver: true,
            },
        ).start(() => (animationPlaying.current = false));
        Animated.spring(opacity, {
            toValue: 0.5,
            useNativeDriver: true,
        }).start();
    };

    const unfoldIcon = () => {
        Animated.spring(
            pan, // Auto-multiplexed
            { toValue: { x: 0, y: 0 }, useNativeDriver: true }, // Back to zero
        ).start();
        Animated.spring(opacity, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    };

    useEffect(() => {
        setVisible(grayInfo?.gray);
    }, [grayInfo]);

    useEffect(() => {
        if (!props.scrollY) {
            return;
        }

        props.scrollY.addListener(() => {
            foldIcon();

            clearTimeout(timer.current);
            // 滚动停止
            timer.current = setTimeout(() => {
                unfoldIcon();
            }, 2000);
        });

        return () => {
            clearTimeout(timer.current);
            props.scrollY.removeAllListeners();
        };
    }, [props.scrollY]);

    useEffect(() => {
        if (!visible) {
            return;
        }

        track('icon');
    }, [visible]);

    useAssistantClose(source);

    // 默认位置的左右需要设置为水平间距
    if (props.defaultPosition?.left) {
        props.defaultPosition.left = HORIZONTAL_PADDING;
    }
    if (props.defaultPosition?.right) {
        props.defaultPosition.right = HORIZONTAL_PADDING;
    }
    const [position, setPosition, getPosition] = useGetState<
        Partial<{
            top: number;
            left: number;
            right: number;
            bottom: number;
        }>
    >(
        props.defaultPosition || {
            right: HORIZONTAL_PADDING,
            bottom: BASE_PADDING,
        },
    );
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => {
                return true;
            },
            onMoveShouldSetPanResponder: (_, gestureState) => {
                const { dx, dy } = gestureState;
                // 只有当移动距离足够大时才启动拖动
                if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
                    setMoving(true);
                    return true;
                }
                return false;
            },
            onPanResponderMove: (_, gestureState) => {
                const { dx, dy } = gestureState;
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    return;
                }
                setPosition({
                    top: gestureState.moveY - baseTop,
                    left: gestureState.moveX,
                });
                startAnimation();
            },
            onPanResponderRelease: (_, gestureState) => {
                setMoving(false);
                const position = getPosition();
                const { dx, dy } = gestureState;

                // 如果移动距离很小，不在这里处理点击，让子组件处理
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    return;
                }

                if (gestureState.moveX > screenWidth / 2) {
                    setPosition({
                        right: HORIZONTAL_PADDING,
                        top: position.top,
                    });
                } else {
                    setPosition({
                        left: HORIZONTAL_PADDING,
                        top: position.top,
                    });
                }
                startAnimation(LayoutAnimation.Presets.spring);
            },
        }),
    ).current;

    // 如果图标过上或者过下则重新布局
    useEffect(() => {
        if (moving || layout.pageY === null) {
            return;
        }
        if (layout.pageY < bounds.top) {
            setPosition({
                ..._.pick(position, ['right', 'left']),
                top: bounds.top,
            });
            startAnimation(LayoutAnimation.Presets.spring);
        }
        if (layout.pageY > bounds.bottom) {
            setPosition({
                ..._.pick(position, ['right', 'left']),
                bottom: BASE_PADDING,
            });
            startAnimation(LayoutAnimation.Presets.spring);
        }
    }, [layout]);

    // 暂时屏蔽
    if (!visible) {
        return null;
    }

    return (
        <View
            style={[styles.container, position]}
            onLayout={onLayout}
            onTouchStart={() => {
                setTimeout(() => {
                    hideTaskReminder();
                }, 200);
            }}
            {...panResponder.panHandlers}
        >
            {/* 引导对齐有问题，在安卓下纵向有偏移，不知道原因 */}
            {visible ? (
                <Animated.View
                    style={{
                        flexDirection: 'row',
                        transform: [
                            { translateX: pan.x },
                            { translateY: pan.y },
                        ],
                        opacity: opacity,
                        alignItems: 'center',
                    }}
                >
                    {isShowTaskReminder && (
                        <TaskReminderBubble
                            onPress={handleTaskReminderPress.run}
                        />
                    )}
                    <TouchableOpacity
                        style={styles.icon}
                        onPress={handleAssistantIconPress.run}
                    >
                        <AssistantIcon size={ICON_SIZE} />
                    </TouchableOpacity>
                </Animated.View>
            ) : null}
        </View>
    );
};

export default Assistant;
